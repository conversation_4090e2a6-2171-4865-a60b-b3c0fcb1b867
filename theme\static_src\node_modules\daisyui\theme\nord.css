:root:has(input.theme-controller[value=nord]:checked),[data-theme="nord"] {
color-scheme: light;
--color-base-100: oklch(95.127% 0.007 260.731);
--color-base-200: oklch(93.299% 0.01 261.788);
--color-base-300: oklch(89.925% 0.016 262.749);
--color-base-content: oklch(32.437% 0.022 264.182);
--color-primary: oklch(59.435% 0.077 254.027);
--color-primary-content: oklch(11.887% 0.015 254.027);
--color-secondary: oklch(69.651% 0.059 248.687);
--color-secondary-content: oklch(13.93% 0.011 248.687);
--color-accent: oklch(77.464% 0.062 217.469);
--color-accent-content: oklch(15.492% 0.012 217.469);
--color-neutral: oklch(45.229% 0.035 264.131);
--color-neutral-content: oklch(89.925% 0.016 262.749);
--color-info: oklch(69.207% 0.062 332.664);
--color-info-content: oklch(13.841% 0.012 332.664);
--color-success: oklch(76.827% 0.074 131.063);
--color-success-content: oklch(15.365% 0.014 131.063);
--color-warning: oklch(85.486% 0.089 84.093);
--color-warning-content: oklch(17.097% 0.017 84.093);
--color-error: oklch(60.61% 0.12 15.341);
--color-error-content: oklch(12.122% 0.024 15.341);
--radius-selector: 1rem;
--radius-field: 0.25rem;
--radius-box: 0.5rem;
--size-selector: 0.25rem;
--size-field: 0.25rem;
--border: 1px;
--depth: 0;
--noise: 0;
}
