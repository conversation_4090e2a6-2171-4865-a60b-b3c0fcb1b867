{% load static %}
<!DOCTYPE html>
<html lang="en" x-data="{ darkMode: localStorage.getItem('theme') === 'dark' }" :data-mdb-theme="darkMode ? 'dark' : 'light'">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Django MDB5</title>
    <!-- MDB5 CSS -->
    <link href="{% static 'mdb/css/mdb.min.css' %}" rel="stylesheet" />
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-body-tertiary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <img src="{% static 'img/Flex.png' %}" height="30" alt="FB15 MES Logo" loading="lazy" />
                B15 MES
            </a>
            <button class="navbar-toggler" type="button" data-mdb-toggle="collapse" data-mdb-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <i class="fas fa-bars"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link active" aria-current="page" href="#">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">Features</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <button class="btn btn-link px-3 me-2" @click="darkMode = !darkMode; localStorage.setItem('theme', darkMode ? 'dark' : 'light')">
                        <span x-show="!darkMode">🌙 暗黑模式</span>
                        <span x-show="darkMode">☀️ 白天模式</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>
    <!-- Navbar -->

    <div class="container mt-5">
        <h1 class="text-primary">Hello, MDB5 with Django!</h1>
        <p>这是一个带有暗黑模式切换功能的 Django MDB5 基础模板。</p>
        <button type="button" class="btn btn-primary">主要按钮</button>
        <button type="button" class="btn btn-secondary">次要按钮</button>

        <h2 class="mt-5">图表区域</h2>
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">图表示例</h5>
                <p class="card-text">这里将放置您的图表内容。请确保您的图表库（如 Chart.js）能够适应主题变化。</p>
                <!-- 您的图表将在这里渲染 -->
                <canvas id="myChart" style="max-width: 600px;"></canvas>
            </div>
        </div>
    </div>

    <!-- MDB5 JS -->
    <script type="text/javascript" src="{% static 'mdb/js/mdb.umd.min.js' %}"></script>
    <!-- Custom JS -->
    <script type="text/javascript" src="{% static 'js/alpine.min.js' %}" defer></script>
    <script type="text/javascript" src="{% static 'js/htmx.min.js' %}"></script>
    <script>
        // 示例：Chart.js 初始化（如果需要）
        // const ctx = document.getElementById('myChart');
        // new Chart(ctx, {
        //     type: 'bar',
        //     data: {
        //         labels: ['Red', 'Blue', 'Yellow', 'Green', 'Purple', 'Orange'],
        //         datasets: [{
        //             label: '# of Votes',
        //             data: [12, 19, 3, 5, 2, 3],
        //             backgroundColor: [
        //                 'rgba(255, 99, 132, 0.2)',
        //                 'rgba(54, 162, 235, 0.2)',
        //                 'rgba(255, 206, 86, 0.2)',
        //                 'rgba(75, 192, 192, 0.2)',
        //                 'rgba(153, 102, 255, 0.2)',
        //                 'rgba(255, 159, 64, 0.2)'
        //             ],
        //             borderColor: [
        //                 'rgba(255, 99, 132, 1)',
        //                 'rgba(54, 162, 235, 1)',
        //                 'rgba(255, 206, 86, 1)',
        //                 'rgba(75, 192, 192, 1)',
        //                 'rgba(153, 102, 255, 1)',
        //                 'rgba(255, 159, 64, 1)'
        //             ],
        //             borderWidth: 1
        //         }]
        //     },
        //     options: {
        //         scales: {
        //             y: {
        //                 beginAtZero: true
        //             }
        //         }
        //     }
        // });
    </script>
</body>
</html>