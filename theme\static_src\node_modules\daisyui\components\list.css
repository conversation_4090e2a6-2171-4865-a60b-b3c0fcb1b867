/*! 🌼 daisyUI 5.0.36 - MIT License */ @layer utilities{.list{flex-direction:column;font-size:.875rem;display:flex;& :where(.list-row){--list-grid-cols:minmax(0,auto)1fr;border-radius:var(--radius-box);word-break:break-word;grid-auto-flow:column;grid-template-columns:var(--list-grid-cols);gap:1rem;padding:1rem;display:grid;position:relative;&:has(.list-col-grow:first-child){--list-grid-cols:1fr}&:has(.list-col-grow:nth-child(2)){--list-grid-cols:minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(3)){--list-grid-cols:minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(4)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(5)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(6)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}& :not(.list-col-wrap){grid-row-start:1}}&>:not(:last-child){&.list-row,& .list-row{&:after{content:"";border-bottom:var(--border)solid;inset-inline:var(--radius-box);border-color:color-mix(in oklab,var(--color-base-content)5%,transparent);position:absolute;bottom:0}}}}.list-col-wrap{grid-row-start:2}@media (width>=640px){.sm\:list{flex-direction:column;font-size:.875rem;display:flex;& :where(.list-row){--list-grid-cols:minmax(0,auto)1fr;border-radius:var(--radius-box);word-break:break-word;grid-auto-flow:column;grid-template-columns:var(--list-grid-cols);gap:1rem;padding:1rem;display:grid;position:relative;&:has(.list-col-grow:first-child){--list-grid-cols:1fr}&:has(.list-col-grow:nth-child(2)){--list-grid-cols:minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(3)){--list-grid-cols:minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(4)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(5)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(6)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}& :not(.list-col-wrap){grid-row-start:1}}&>:not(:last-child){&.list-row,& .list-row{&:after{content:"";border-bottom:var(--border)solid;inset-inline:var(--radius-box);border-color:color-mix(in oklab,var(--color-base-content)5%,transparent);position:absolute;bottom:0}}}}.sm\:list-col-wrap{grid-row-start:2}}@media (width>=768px){.md\:list{flex-direction:column;font-size:.875rem;display:flex;& :where(.list-row){--list-grid-cols:minmax(0,auto)1fr;border-radius:var(--radius-box);word-break:break-word;grid-auto-flow:column;grid-template-columns:var(--list-grid-cols);gap:1rem;padding:1rem;display:grid;position:relative;&:has(.list-col-grow:first-child){--list-grid-cols:1fr}&:has(.list-col-grow:nth-child(2)){--list-grid-cols:minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(3)){--list-grid-cols:minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(4)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(5)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(6)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}& :not(.list-col-wrap){grid-row-start:1}}&>:not(:last-child){&.list-row,& .list-row{&:after{content:"";border-bottom:var(--border)solid;inset-inline:var(--radius-box);border-color:color-mix(in oklab,var(--color-base-content)5%,transparent);position:absolute;bottom:0}}}}.md\:list-col-wrap{grid-row-start:2}}@media (width>=1024px){.lg\:list{flex-direction:column;font-size:.875rem;display:flex;& :where(.list-row){--list-grid-cols:minmax(0,auto)1fr;border-radius:var(--radius-box);word-break:break-word;grid-auto-flow:column;grid-template-columns:var(--list-grid-cols);gap:1rem;padding:1rem;display:grid;position:relative;&:has(.list-col-grow:first-child){--list-grid-cols:1fr}&:has(.list-col-grow:nth-child(2)){--list-grid-cols:minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(3)){--list-grid-cols:minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(4)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(5)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(6)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}& :not(.list-col-wrap){grid-row-start:1}}&>:not(:last-child){&.list-row,& .list-row{&:after{content:"";border-bottom:var(--border)solid;inset-inline:var(--radius-box);border-color:color-mix(in oklab,var(--color-base-content)5%,transparent);position:absolute;bottom:0}}}}.lg\:list-col-wrap{grid-row-start:2}}@media (width>=1280px){.xl\:list{flex-direction:column;font-size:.875rem;display:flex;& :where(.list-row){--list-grid-cols:minmax(0,auto)1fr;border-radius:var(--radius-box);word-break:break-word;grid-auto-flow:column;grid-template-columns:var(--list-grid-cols);gap:1rem;padding:1rem;display:grid;position:relative;&:has(.list-col-grow:first-child){--list-grid-cols:1fr}&:has(.list-col-grow:nth-child(2)){--list-grid-cols:minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(3)){--list-grid-cols:minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(4)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(5)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(6)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}& :not(.list-col-wrap){grid-row-start:1}}&>:not(:last-child){&.list-row,& .list-row{&:after{content:"";border-bottom:var(--border)solid;inset-inline:var(--radius-box);border-color:color-mix(in oklab,var(--color-base-content)5%,transparent);position:absolute;bottom:0}}}}.xl\:list-col-wrap{grid-row-start:2}}@media (width>=1536px){.\32 xl\:list{flex-direction:column;font-size:.875rem;display:flex;& :where(.list-row){--list-grid-cols:minmax(0,auto)1fr;border-radius:var(--radius-box);word-break:break-word;grid-auto-flow:column;grid-template-columns:var(--list-grid-cols);gap:1rem;padding:1rem;display:grid;position:relative;&:has(.list-col-grow:first-child){--list-grid-cols:1fr}&:has(.list-col-grow:nth-child(2)){--list-grid-cols:minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(3)){--list-grid-cols:minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(4)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(5)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}&:has(.list-col-grow:nth-child(6)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}& :not(.list-col-wrap){grid-row-start:1}}&>:not(:last-child){&.list-row,& .list-row{&:after{content:"";border-bottom:var(--border)solid;inset-inline:var(--radius-box);border-color:color-mix(in oklab,var(--color-base-content)5%,transparent);position:absolute;bottom:0}}}}.\32 xl\:list-col-wrap{grid-row-start:2}}}