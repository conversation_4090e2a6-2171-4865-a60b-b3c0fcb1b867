/*! 🌼 daisyUI 5.0.36 - MIT License */ @layer utilities{.link{cursor:pointer;text-decoration-line:underline;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-offset:2px;outline:2px solid}}.link-hover{text-decoration-line:none;&:hover{@media (hover:hover){&{text-decoration-line:underline}}}}.link-primary{color:var(--color-primary);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-primary)80%,#000)}}}.link-secondary{color:var(--color-secondary);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-secondary)80%,#000)}}}.link-accent{color:var(--color-accent);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-accent)80%,#000)}}}.link-neutral{color:var(--color-neutral);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-neutral)80%,#000)}}}.link-success{color:var(--color-success);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-success)80%,#000)}}}.link-info{color:var(--color-info);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-info)80%,#000)}}}.link-warning{color:var(--color-warning);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-warning)80%,#000)}}}.link-error{color:var(--color-error);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-error)80%,#000)}}}@media (width>=640px){.sm\:link{cursor:pointer;text-decoration-line:underline;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-offset:2px;outline:2px solid}}.sm\:link-hover{text-decoration-line:none;&:hover{@media (hover:hover){&{text-decoration-line:underline}}}}.sm\:link-primary{color:var(--color-primary);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-primary)80%,#000)}}}.sm\:link-secondary{color:var(--color-secondary);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-secondary)80%,#000)}}}.sm\:link-accent{color:var(--color-accent);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-accent)80%,#000)}}}.sm\:link-neutral{color:var(--color-neutral);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-neutral)80%,#000)}}}.sm\:link-success{color:var(--color-success);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-success)80%,#000)}}}.sm\:link-info{color:var(--color-info);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-info)80%,#000)}}}.sm\:link-warning{color:var(--color-warning);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-warning)80%,#000)}}}.sm\:link-error{color:var(--color-error);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-error)80%,#000)}}}}@media (width>=768px){.md\:link{cursor:pointer;text-decoration-line:underline;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-offset:2px;outline:2px solid}}.md\:link-hover{text-decoration-line:none;&:hover{@media (hover:hover){&{text-decoration-line:underline}}}}.md\:link-primary{color:var(--color-primary);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-primary)80%,#000)}}}.md\:link-secondary{color:var(--color-secondary);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-secondary)80%,#000)}}}.md\:link-accent{color:var(--color-accent);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-accent)80%,#000)}}}.md\:link-neutral{color:var(--color-neutral);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-neutral)80%,#000)}}}.md\:link-success{color:var(--color-success);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-success)80%,#000)}}}.md\:link-info{color:var(--color-info);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-info)80%,#000)}}}.md\:link-warning{color:var(--color-warning);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-warning)80%,#000)}}}.md\:link-error{color:var(--color-error);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-error)80%,#000)}}}}@media (width>=1024px){.lg\:link{cursor:pointer;text-decoration-line:underline;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-offset:2px;outline:2px solid}}.lg\:link-hover{text-decoration-line:none;&:hover{@media (hover:hover){&{text-decoration-line:underline}}}}.lg\:link-primary{color:var(--color-primary);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-primary)80%,#000)}}}.lg\:link-secondary{color:var(--color-secondary);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-secondary)80%,#000)}}}.lg\:link-accent{color:var(--color-accent);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-accent)80%,#000)}}}.lg\:link-neutral{color:var(--color-neutral);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-neutral)80%,#000)}}}.lg\:link-success{color:var(--color-success);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-success)80%,#000)}}}.lg\:link-info{color:var(--color-info);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-info)80%,#000)}}}.lg\:link-warning{color:var(--color-warning);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-warning)80%,#000)}}}.lg\:link-error{color:var(--color-error);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-error)80%,#000)}}}}@media (width>=1280px){.xl\:link{cursor:pointer;text-decoration-line:underline;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-offset:2px;outline:2px solid}}.xl\:link-hover{text-decoration-line:none;&:hover{@media (hover:hover){&{text-decoration-line:underline}}}}.xl\:link-primary{color:var(--color-primary);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-primary)80%,#000)}}}.xl\:link-secondary{color:var(--color-secondary);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-secondary)80%,#000)}}}.xl\:link-accent{color:var(--color-accent);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-accent)80%,#000)}}}.xl\:link-neutral{color:var(--color-neutral);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-neutral)80%,#000)}}}.xl\:link-success{color:var(--color-success);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-success)80%,#000)}}}.xl\:link-info{color:var(--color-info);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-info)80%,#000)}}}.xl\:link-warning{color:var(--color-warning);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-warning)80%,#000)}}}.xl\:link-error{color:var(--color-error);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-error)80%,#000)}}}}@media (width>=1536px){.\32 xl\:link{cursor:pointer;text-decoration-line:underline;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible{outline-offset:2px;outline:2px solid}}.\32 xl\:link-hover{text-decoration-line:none;&:hover{@media (hover:hover){&{text-decoration-line:underline}}}}.\32 xl\:link-primary{color:var(--color-primary);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-primary)80%,#000)}}}.\32 xl\:link-secondary{color:var(--color-secondary);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-secondary)80%,#000)}}}.\32 xl\:link-accent{color:var(--color-accent);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-accent)80%,#000)}}}.\32 xl\:link-neutral{color:var(--color-neutral);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-neutral)80%,#000)}}}.\32 xl\:link-success{color:var(--color-success);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-success)80%,#000)}}}.\32 xl\:link-info{color:var(--color-info);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-info)80%,#000)}}}.\32 xl\:link-warning{color:var(--color-warning);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-warning)80%,#000)}}}.\32 xl\:link-error{color:var(--color-error);@media (hover:hover){&:hover{color:color-mix(in oklab,var(--color-error)80%,#000)}}}}}