{% extends "base.html" %}

{% block title %}DaisyUI Example{% endblock %}

{% block content %}
<div class="grid gap-4">
    <h1 class="text-3xl font-bold">DaisyUI Components</h1>
    
    <div class="card w-96 bg-base-100 shadow-xl">
        <div class="card-body">
            <h2 class="card-title">Card Title</h2>
            <p>This is a DaisyUI card component.</p>
            <div class="card-actions justify-end">
                <button class="btn btn-primary">Button</button>
            </div>
        </div>
    </div>
    
    <div class="flex gap-2">
        <button class="btn">Default</button>
        <button class="btn btn-primary">Primary</button>
        <button class="btn btn-secondary">Secondary</button>
        <button class="btn btn-accent">Accent</button>
    </div>
    
    <div class="flex gap-2">
        <span class="badge">Badge</span>
        <span class="badge badge-primary">Primary</span>
        <span class="badge badge-secondary">Secondary</span>
    </div>
</div>
{% endblock %}