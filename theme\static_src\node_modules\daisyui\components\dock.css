/*! 🌼 daisyUI 5.0.36 - MIT License */ @layer utilities{.dock{z-index:1;background-color:var(--color-base-100);color:currentColor;border-top:.5px solid color-mix(in oklab,var(--color-base-content)5%,#0000);width:100%;height:4rem;height:calc(4rem + env(safe-area-inset-bottom));padding:.5rem;padding-bottom:env(safe-area-inset-bottom);flex-direction:row;justify-content:space-around;align-items:center;display:flex;position:fixed;bottom:0;left:0;right:0;&>*{cursor:pointer;border-radius:var(--radius-box);background-color:#0000;flex-direction:column;flex-shrink:1;flex-basis:100%;justify-content:center;align-items:center;gap:1px;max-width:8rem;height:100%;margin-bottom:.5rem;transition:opacity .2s ease-out;display:flex;position:relative;@media (hover:hover){&:hover{opacity:.8}}&[aria-disabled=true],&[disabled]{&,&:hover{pointer-events:none;color:color-mix(in oklab,var(--color-base-content)10%,transparent);opacity:1}}& .dock-label{font-size:.6875rem}&:after{content:"";background-color:#0000;border-top:3px solid #0000;border-radius:3.40282e38px;width:1.5rem;height:.25rem;transition:background-color .1s ease-out,text-color .1s ease-out,width .1s ease-out;position:absolute;bottom:.2rem}}}.dock-active{&:after{color:currentColor;background-color:currentColor;width:2.5rem}}.dock-xs{height:3rem;height:calc(3rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:-.1rem}}& .dock-label{font-size:.625rem}}.dock-sm{height:3.5rem;height:calc(3.5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:-.1rem}}& .dock-label{font-size:.625rem}}.dock-md{height:4rem;height:calc(4rem + env(safe-area-inset-bottom));& .dock-label{font-size:.6875rem}}.dock-lg{height:4.5rem;height:calc(4.5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:.4rem}}& .dock-label{font-size:.6875rem}}.dock-xl{height:5rem;height:calc(5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:.4rem}}& .dock-label{font-size:.75rem}}@media (width>=640px){.sm\:dock{z-index:1;background-color:var(--color-base-100);color:currentColor;border-top:.5px solid color-mix(in oklab,var(--color-base-content)5%,#0000);width:100%;height:4rem;height:calc(4rem + env(safe-area-inset-bottom));padding:.5rem;padding-bottom:env(safe-area-inset-bottom);flex-direction:row;justify-content:space-around;align-items:center;display:flex;position:fixed;bottom:0;left:0;right:0;&>*{cursor:pointer;border-radius:var(--radius-box);background-color:#0000;flex-direction:column;flex-shrink:1;flex-basis:100%;justify-content:center;align-items:center;gap:1px;max-width:8rem;height:100%;margin-bottom:.5rem;transition:opacity .2s ease-out;display:flex;position:relative;@media (hover:hover){&:hover{opacity:.8}}&[aria-disabled=true],&[disabled]{&,&:hover{pointer-events:none;color:color-mix(in oklab,var(--color-base-content)10%,transparent);opacity:1}}& .dock-label{font-size:.6875rem}&:after{content:"";background-color:#0000;border-top:3px solid #0000;border-radius:3.40282e38px;width:1.5rem;height:.25rem;transition:background-color .1s ease-out,text-color .1s ease-out,width .1s ease-out;position:absolute;bottom:.2rem}}}.sm\:dock-active{&:after{color:currentColor;background-color:currentColor;width:2.5rem}}.sm\:dock-xs{height:3rem;height:calc(3rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:-.1rem}}& .dock-label{font-size:.625rem}}.sm\:dock-sm{height:3.5rem;height:calc(3.5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:-.1rem}}& .dock-label{font-size:.625rem}}.sm\:dock-md{height:4rem;height:calc(4rem + env(safe-area-inset-bottom));& .dock-label{font-size:.6875rem}}.sm\:dock-lg{height:4.5rem;height:calc(4.5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:.4rem}}& .dock-label{font-size:.6875rem}}.sm\:dock-xl{height:5rem;height:calc(5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:.4rem}}& .dock-label{font-size:.75rem}}}@media (width>=768px){.md\:dock{z-index:1;background-color:var(--color-base-100);color:currentColor;border-top:.5px solid color-mix(in oklab,var(--color-base-content)5%,#0000);width:100%;height:4rem;height:calc(4rem + env(safe-area-inset-bottom));padding:.5rem;padding-bottom:env(safe-area-inset-bottom);flex-direction:row;justify-content:space-around;align-items:center;display:flex;position:fixed;bottom:0;left:0;right:0;&>*{cursor:pointer;border-radius:var(--radius-box);background-color:#0000;flex-direction:column;flex-shrink:1;flex-basis:100%;justify-content:center;align-items:center;gap:1px;max-width:8rem;height:100%;margin-bottom:.5rem;transition:opacity .2s ease-out;display:flex;position:relative;@media (hover:hover){&:hover{opacity:.8}}&[aria-disabled=true],&[disabled]{&,&:hover{pointer-events:none;color:color-mix(in oklab,var(--color-base-content)10%,transparent);opacity:1}}& .dock-label{font-size:.6875rem}&:after{content:"";background-color:#0000;border-top:3px solid #0000;border-radius:3.40282e38px;width:1.5rem;height:.25rem;transition:background-color .1s ease-out,text-color .1s ease-out,width .1s ease-out;position:absolute;bottom:.2rem}}}.md\:dock-active{&:after{color:currentColor;background-color:currentColor;width:2.5rem}}.md\:dock-xs{height:3rem;height:calc(3rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:-.1rem}}& .dock-label{font-size:.625rem}}.md\:dock-sm{height:3.5rem;height:calc(3.5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:-.1rem}}& .dock-label{font-size:.625rem}}.md\:dock-md{height:4rem;height:calc(4rem + env(safe-area-inset-bottom));& .dock-label{font-size:.6875rem}}.md\:dock-lg{height:4.5rem;height:calc(4.5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:.4rem}}& .dock-label{font-size:.6875rem}}.md\:dock-xl{height:5rem;height:calc(5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:.4rem}}& .dock-label{font-size:.75rem}}}@media (width>=1024px){.lg\:dock{z-index:1;background-color:var(--color-base-100);color:currentColor;border-top:.5px solid color-mix(in oklab,var(--color-base-content)5%,#0000);width:100%;height:4rem;height:calc(4rem + env(safe-area-inset-bottom));padding:.5rem;padding-bottom:env(safe-area-inset-bottom);flex-direction:row;justify-content:space-around;align-items:center;display:flex;position:fixed;bottom:0;left:0;right:0;&>*{cursor:pointer;border-radius:var(--radius-box);background-color:#0000;flex-direction:column;flex-shrink:1;flex-basis:100%;justify-content:center;align-items:center;gap:1px;max-width:8rem;height:100%;margin-bottom:.5rem;transition:opacity .2s ease-out;display:flex;position:relative;@media (hover:hover){&:hover{opacity:.8}}&[aria-disabled=true],&[disabled]{&,&:hover{pointer-events:none;color:color-mix(in oklab,var(--color-base-content)10%,transparent);opacity:1}}& .dock-label{font-size:.6875rem}&:after{content:"";background-color:#0000;border-top:3px solid #0000;border-radius:3.40282e38px;width:1.5rem;height:.25rem;transition:background-color .1s ease-out,text-color .1s ease-out,width .1s ease-out;position:absolute;bottom:.2rem}}}.lg\:dock-active{&:after{color:currentColor;background-color:currentColor;width:2.5rem}}.lg\:dock-xs{height:3rem;height:calc(3rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:-.1rem}}& .dock-label{font-size:.625rem}}.lg\:dock-sm{height:3.5rem;height:calc(3.5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:-.1rem}}& .dock-label{font-size:.625rem}}.lg\:dock-md{height:4rem;height:calc(4rem + env(safe-area-inset-bottom));& .dock-label{font-size:.6875rem}}.lg\:dock-lg{height:4.5rem;height:calc(4.5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:.4rem}}& .dock-label{font-size:.6875rem}}.lg\:dock-xl{height:5rem;height:calc(5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:.4rem}}& .dock-label{font-size:.75rem}}}@media (width>=1280px){.xl\:dock{z-index:1;background-color:var(--color-base-100);color:currentColor;border-top:.5px solid color-mix(in oklab,var(--color-base-content)5%,#0000);width:100%;height:4rem;height:calc(4rem + env(safe-area-inset-bottom));padding:.5rem;padding-bottom:env(safe-area-inset-bottom);flex-direction:row;justify-content:space-around;align-items:center;display:flex;position:fixed;bottom:0;left:0;right:0;&>*{cursor:pointer;border-radius:var(--radius-box);background-color:#0000;flex-direction:column;flex-shrink:1;flex-basis:100%;justify-content:center;align-items:center;gap:1px;max-width:8rem;height:100%;margin-bottom:.5rem;transition:opacity .2s ease-out;display:flex;position:relative;@media (hover:hover){&:hover{opacity:.8}}&[aria-disabled=true],&[disabled]{&,&:hover{pointer-events:none;color:color-mix(in oklab,var(--color-base-content)10%,transparent);opacity:1}}& .dock-label{font-size:.6875rem}&:after{content:"";background-color:#0000;border-top:3px solid #0000;border-radius:3.40282e38px;width:1.5rem;height:.25rem;transition:background-color .1s ease-out,text-color .1s ease-out,width .1s ease-out;position:absolute;bottom:.2rem}}}.xl\:dock-active{&:after{color:currentColor;background-color:currentColor;width:2.5rem}}.xl\:dock-xs{height:3rem;height:calc(3rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:-.1rem}}& .dock-label{font-size:.625rem}}.xl\:dock-sm{height:3.5rem;height:calc(3.5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:-.1rem}}& .dock-label{font-size:.625rem}}.xl\:dock-md{height:4rem;height:calc(4rem + env(safe-area-inset-bottom));& .dock-label{font-size:.6875rem}}.xl\:dock-lg{height:4.5rem;height:calc(4.5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:.4rem}}& .dock-label{font-size:.6875rem}}.xl\:dock-xl{height:5rem;height:calc(5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:.4rem}}& .dock-label{font-size:.75rem}}}@media (width>=1536px){.\32 xl\:dock{z-index:1;background-color:var(--color-base-100);color:currentColor;border-top:.5px solid color-mix(in oklab,var(--color-base-content)5%,#0000);width:100%;height:4rem;height:calc(4rem + env(safe-area-inset-bottom));padding:.5rem;padding-bottom:env(safe-area-inset-bottom);flex-direction:row;justify-content:space-around;align-items:center;display:flex;position:fixed;bottom:0;left:0;right:0;&>*{cursor:pointer;border-radius:var(--radius-box);background-color:#0000;flex-direction:column;flex-shrink:1;flex-basis:100%;justify-content:center;align-items:center;gap:1px;max-width:8rem;height:100%;margin-bottom:.5rem;transition:opacity .2s ease-out;display:flex;position:relative;@media (hover:hover){&:hover{opacity:.8}}&[aria-disabled=true],&[disabled]{&,&:hover{pointer-events:none;color:color-mix(in oklab,var(--color-base-content)10%,transparent);opacity:1}}& .dock-label{font-size:.6875rem}&:after{content:"";background-color:#0000;border-top:3px solid #0000;border-radius:3.40282e38px;width:1.5rem;height:.25rem;transition:background-color .1s ease-out,text-color .1s ease-out,width .1s ease-out;position:absolute;bottom:.2rem}}}.\32 xl\:dock-active{&:after{color:currentColor;background-color:currentColor;width:2.5rem}}.\32 xl\:dock-xs{height:3rem;height:calc(3rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:-.1rem}}& .dock-label{font-size:.625rem}}.\32 xl\:dock-sm{height:3.5rem;height:calc(3.5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:-.1rem}}& .dock-label{font-size:.625rem}}.\32 xl\:dock-md{height:4rem;height:calc(4rem + env(safe-area-inset-bottom));& .dock-label{font-size:.6875rem}}.\32 xl\:dock-lg{height:4.5rem;height:calc(4.5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:.4rem}}& .dock-label{font-size:.6875rem}}.\32 xl\:dock-xl{height:5rem;height:calc(5rem + env(safe-area-inset-bottom));& .dock-active{&:after{bottom:.4rem}}& .dock-label{font-size:.75rem}}}}