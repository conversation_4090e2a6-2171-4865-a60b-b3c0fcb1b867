/*! 🌼 daisyUI 5.0.36 - MIT License */ @layer utilities{.radio{cursor:pointer;appearance:none;vertical-align:middle;border:var(--border)solid var(--input-color,color-mix(in srgb,currentColor 20%,#0000));box-shadow:0 1px oklch(0% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-selector,.25rem)*6);width:var(--size);height:var(--size);color:var(--input-color,currentColor);border-radius:3.40282e38px;flex-shrink:0;padding:.25rem;position:relative;&:before{--tw-content:"";content:var(--tw-content);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);border-radius:3.40282e38px;width:100%;height:100%;display:block}&:focus-visible{outline:2px solid}&:checked,&[aria-checked=true]{background-color:var(--color-base-100);border-color:currentColor;animation:.2s ease-out radio;&:before{box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px oklch(0% 0 0/calc(var(--depth)*.1));background-color:currentColor}@media (forced-colors:active){&:before{outline-style:var(--tw-outline-style);outline-offset:calc(1px*-1);outline-width:1px}}@media print{&:before{outline-offset:-1rem;outline:.25rem solid}}}}.radio-primary{--input-color:var(--color-primary)}.radio-secondary{--input-color:var(--color-secondary)}.radio-accent{--input-color:var(--color-accent)}.radio-neutral{--input-color:var(--color-neutral)}.radio-info{--input-color:var(--color-info)}.radio-success{--input-color:var(--color-success)}.radio-warning{--input-color:var(--color-warning)}.radio-error{--input-color:var(--color-error)}.radio:disabled{cursor:not-allowed;opacity:.2}.radio-xs{padding:.125rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*4)}}.radio-sm{padding:.1875rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*5)}}.radio-md{padding:.25rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*6)}}.radio-lg{padding:.3125rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*7)}}.radio-xl{padding:.375rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*8)}}@keyframes radio{0%{padding:5px}50%{padding:3px}}@media (width>=640px){.sm\:radio{cursor:pointer;appearance:none;vertical-align:middle;border:var(--border)solid var(--input-color,color-mix(in srgb,currentColor 20%,#0000));box-shadow:0 1px oklch(0% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-selector,.25rem)*6);width:var(--size);height:var(--size);color:var(--input-color,currentColor);border-radius:3.40282e38px;flex-shrink:0;padding:.25rem;position:relative;&:before{--tw-content:"";content:var(--tw-content);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);border-radius:3.40282e38px;width:100%;height:100%;display:block}&:focus-visible{outline:2px solid}&:checked,&[aria-checked=true]{background-color:var(--color-base-100);border-color:currentColor;animation:.2s ease-out radio;&:before{box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px oklch(0% 0 0/calc(var(--depth)*.1));background-color:currentColor}@media (forced-colors:active){&:before{outline-style:var(--tw-outline-style);outline-offset:calc(1px*-1);outline-width:1px}}@media print{&:before{outline-offset:-1rem;outline:.25rem solid}}}}.sm\:radio-primary{--input-color:var(--color-primary)}.sm\:radio-secondary{--input-color:var(--color-secondary)}.sm\:radio-accent{--input-color:var(--color-accent)}.sm\:radio-neutral{--input-color:var(--color-neutral)}.sm\:radio-info{--input-color:var(--color-info)}.sm\:radio-success{--input-color:var(--color-success)}.sm\:radio-warning{--input-color:var(--color-warning)}.sm\:radio-error{--input-color:var(--color-error)}.sm\:radio:disabled{cursor:not-allowed;opacity:.2}.sm\:radio-xs{padding:.125rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*4)}}.sm\:radio-sm{padding:.1875rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*5)}}.sm\:radio-md{padding:.25rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*6)}}.sm\:radio-lg{padding:.3125rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*7)}}.sm\:radio-xl{padding:.375rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*8)}}}@media (width>=768px){.md\:radio{cursor:pointer;appearance:none;vertical-align:middle;border:var(--border)solid var(--input-color,color-mix(in srgb,currentColor 20%,#0000));box-shadow:0 1px oklch(0% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-selector,.25rem)*6);width:var(--size);height:var(--size);color:var(--input-color,currentColor);border-radius:3.40282e38px;flex-shrink:0;padding:.25rem;position:relative;&:before{--tw-content:"";content:var(--tw-content);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);border-radius:3.40282e38px;width:100%;height:100%;display:block}&:focus-visible{outline:2px solid}&:checked,&[aria-checked=true]{background-color:var(--color-base-100);border-color:currentColor;animation:.2s ease-out radio;&:before{box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px oklch(0% 0 0/calc(var(--depth)*.1));background-color:currentColor}@media (forced-colors:active){&:before{outline-style:var(--tw-outline-style);outline-offset:calc(1px*-1);outline-width:1px}}@media print{&:before{outline-offset:-1rem;outline:.25rem solid}}}}.md\:radio-primary{--input-color:var(--color-primary)}.md\:radio-secondary{--input-color:var(--color-secondary)}.md\:radio-accent{--input-color:var(--color-accent)}.md\:radio-neutral{--input-color:var(--color-neutral)}.md\:radio-info{--input-color:var(--color-info)}.md\:radio-success{--input-color:var(--color-success)}.md\:radio-warning{--input-color:var(--color-warning)}.md\:radio-error{--input-color:var(--color-error)}.md\:radio:disabled{cursor:not-allowed;opacity:.2}.md\:radio-xs{padding:.125rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*4)}}.md\:radio-sm{padding:.1875rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*5)}}.md\:radio-md{padding:.25rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*6)}}.md\:radio-lg{padding:.3125rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*7)}}.md\:radio-xl{padding:.375rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*8)}}}@media (width>=1024px){.lg\:radio{cursor:pointer;appearance:none;vertical-align:middle;border:var(--border)solid var(--input-color,color-mix(in srgb,currentColor 20%,#0000));box-shadow:0 1px oklch(0% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-selector,.25rem)*6);width:var(--size);height:var(--size);color:var(--input-color,currentColor);border-radius:3.40282e38px;flex-shrink:0;padding:.25rem;position:relative;&:before{--tw-content:"";content:var(--tw-content);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);border-radius:3.40282e38px;width:100%;height:100%;display:block}&:focus-visible{outline:2px solid}&:checked,&[aria-checked=true]{background-color:var(--color-base-100);border-color:currentColor;animation:.2s ease-out radio;&:before{box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px oklch(0% 0 0/calc(var(--depth)*.1));background-color:currentColor}@media (forced-colors:active){&:before{outline-style:var(--tw-outline-style);outline-offset:calc(1px*-1);outline-width:1px}}@media print{&:before{outline-offset:-1rem;outline:.25rem solid}}}}.lg\:radio-primary{--input-color:var(--color-primary)}.lg\:radio-secondary{--input-color:var(--color-secondary)}.lg\:radio-accent{--input-color:var(--color-accent)}.lg\:radio-neutral{--input-color:var(--color-neutral)}.lg\:radio-info{--input-color:var(--color-info)}.lg\:radio-success{--input-color:var(--color-success)}.lg\:radio-warning{--input-color:var(--color-warning)}.lg\:radio-error{--input-color:var(--color-error)}.lg\:radio:disabled{cursor:not-allowed;opacity:.2}.lg\:radio-xs{padding:.125rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*4)}}.lg\:radio-sm{padding:.1875rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*5)}}.lg\:radio-md{padding:.25rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*6)}}.lg\:radio-lg{padding:.3125rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*7)}}.lg\:radio-xl{padding:.375rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*8)}}}@media (width>=1280px){.xl\:radio{cursor:pointer;appearance:none;vertical-align:middle;border:var(--border)solid var(--input-color,color-mix(in srgb,currentColor 20%,#0000));box-shadow:0 1px oklch(0% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-selector,.25rem)*6);width:var(--size);height:var(--size);color:var(--input-color,currentColor);border-radius:3.40282e38px;flex-shrink:0;padding:.25rem;position:relative;&:before{--tw-content:"";content:var(--tw-content);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);border-radius:3.40282e38px;width:100%;height:100%;display:block}&:focus-visible{outline:2px solid}&:checked,&[aria-checked=true]{background-color:var(--color-base-100);border-color:currentColor;animation:.2s ease-out radio;&:before{box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px oklch(0% 0 0/calc(var(--depth)*.1));background-color:currentColor}@media (forced-colors:active){&:before{outline-style:var(--tw-outline-style);outline-offset:calc(1px*-1);outline-width:1px}}@media print{&:before{outline-offset:-1rem;outline:.25rem solid}}}}.xl\:radio-primary{--input-color:var(--color-primary)}.xl\:radio-secondary{--input-color:var(--color-secondary)}.xl\:radio-accent{--input-color:var(--color-accent)}.xl\:radio-neutral{--input-color:var(--color-neutral)}.xl\:radio-info{--input-color:var(--color-info)}.xl\:radio-success{--input-color:var(--color-success)}.xl\:radio-warning{--input-color:var(--color-warning)}.xl\:radio-error{--input-color:var(--color-error)}.xl\:radio:disabled{cursor:not-allowed;opacity:.2}.xl\:radio-xs{padding:.125rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*4)}}.xl\:radio-sm{padding:.1875rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*5)}}.xl\:radio-md{padding:.25rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*6)}}.xl\:radio-lg{padding:.3125rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*7)}}.xl\:radio-xl{padding:.375rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*8)}}}@media (width>=1536px){.\32 xl\:radio{cursor:pointer;appearance:none;vertical-align:middle;border:var(--border)solid var(--input-color,color-mix(in srgb,currentColor 20%,#0000));box-shadow:0 1px oklch(0% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-selector,.25rem)*6);width:var(--size);height:var(--size);color:var(--input-color,currentColor);border-radius:3.40282e38px;flex-shrink:0;padding:.25rem;position:relative;&:before{--tw-content:"";content:var(--tw-content);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);border-radius:3.40282e38px;width:100%;height:100%;display:block}&:focus-visible{outline:2px solid}&:checked,&[aria-checked=true]{background-color:var(--color-base-100);border-color:currentColor;animation:.2s ease-out radio;&:before{box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px oklch(0% 0 0/calc(var(--depth)*.1));background-color:currentColor}@media (forced-colors:active){&:before{outline-style:var(--tw-outline-style);outline-offset:calc(1px*-1);outline-width:1px}}@media print{&:before{outline-offset:-1rem;outline:.25rem solid}}}}.\32 xl\:radio-primary{--input-color:var(--color-primary)}.\32 xl\:radio-secondary{--input-color:var(--color-secondary)}.\32 xl\:radio-accent{--input-color:var(--color-accent)}.\32 xl\:radio-neutral{--input-color:var(--color-neutral)}.\32 xl\:radio-info{--input-color:var(--color-info)}.\32 xl\:radio-success{--input-color:var(--color-success)}.\32 xl\:radio-warning{--input-color:var(--color-warning)}.\32 xl\:radio-error{--input-color:var(--color-error)}.\32 xl\:radio:disabled{cursor:not-allowed;opacity:.2}.\32 xl\:radio-xs{padding:.125rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*4)}}.\32 xl\:radio-sm{padding:.1875rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*5)}}.\32 xl\:radio-md{padding:.25rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*6)}}.\32 xl\:radio-lg{padding:.3125rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*7)}}.\32 xl\:radio-xl{padding:.375rem;&[type=radio]{--size:calc(var(--size-selector,.25rem)*8)}}}}