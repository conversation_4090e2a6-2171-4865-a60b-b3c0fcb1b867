/*! 🌼 daisyUI 5.0.36 - MIT License */ @layer utilities{.filter{flex-wrap:wrap;display:flex;& input[type=radio]{width:auto}& input{opacity:1;transition:margin .1s,opacity .3s,padding .3s,border-width .1s;overflow:hidden;scale:1;&:not(:last-child){margin-inline-end:.25rem}&.filter-reset{aspect-ratio:1;&:after{content:"×"}}}&:not(:has(input:checked:not(.filter-reset))){& .filter-reset,& input[type=reset]{opacity:0;border-width:0;width:0;margin-inline:0;padding-inline:0;scale:0}}&:has(input:checked:not(.filter-reset)){& input:not(:checked,.filter-reset,input[type=reset]){opacity:0;border-width:0;width:0;margin-inline:0;padding-inline:0;scale:0}}}}