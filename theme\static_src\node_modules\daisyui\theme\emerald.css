:root:has(input.theme-controller[value=emerald]:checked),[data-theme="emerald"] {
color-scheme: light;
--color-base-100: oklch(100% 0 0);
--color-base-200: oklch(93% 0 0);
--color-base-300: oklch(86% 0 0);
--color-base-content: oklch(35.519% 0.032 262.988);
--color-primary: oklch(76.662% 0.135 153.45);
--color-primary-content: oklch(33.387% 0.04 162.24);
--color-secondary: oklch(61.302% 0.202 261.294);
--color-secondary-content: oklch(100% 0 0);
--color-accent: oklch(72.772% 0.149 33.2);
--color-accent-content: oklch(0% 0 0);
--color-neutral: oklch(35.519% 0.032 262.988);
--color-neutral-content: oklch(98.462% 0.001 247.838);
--color-info: oklch(72.06% 0.191 231.6);
--color-info-content: oklch(0% 0 0);
--color-success: oklch(64.8% 0.15 160);
--color-success-content: oklch(0% 0 0);
--color-warning: oklch(84.71% 0.199 83.87);
--color-warning-content: oklch(0% 0 0);
--color-error: oklch(71.76% 0.221 22.18);
--color-error-content: oklch(0% 0 0);
--radius-selector: 1rem;
--radius-field: 0.5rem;
--radius-box: 1rem;
--size-selector: 0.25rem;
--size-field: 0.25rem;
--border: 1px;
--depth: 0;
--noise: 0;
}
