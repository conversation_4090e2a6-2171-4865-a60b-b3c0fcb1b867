/*! 🌼 daisyUI 5.0.36 - MIT License */ @layer utilities{.menu{--menu-active-fg:var(--color-neutral-content);--menu-active-bg:var(--color-neutral);flex-flow:column wrap;width:fit-content;padding:.5rem;font-size:.875rem;display:flex;& :where(li ul){white-space:nowrap;margin-inline-start:1rem;padding-inline-start:.5rem;position:relative;&:before{inset-inline-start:0;background-color:var(--color-base-content);opacity:.1;width:var(--border);content:"";position:absolute;top:.75rem;bottom:.75rem}}& :where(li>.menu-dropdown:not(.menu-dropdown-show)){display:none}& :where(li:not(.menu-title)>:not(ul,details,.menu-title,.btn)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);text-align:start;text-wrap:balance;user-select:none;grid-auto-columns:minmax(auto,max-content) auto max-content;grid-auto-flow:column;align-content:flex-start;align-items:center;gap:.5rem;padding-block:.375rem;padding-inline:.75rem;transition-property:color,background-color,box-shadow;transition-duration:.2s;transition-timing-function:cubic-bezier(0,0,.2,1);display:grid}& :where(li>details>summary){--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}&::-webkit-details-marker{display:none}}& :where(li>details>summary),& :where(li>.menu-dropdown-toggle){&:after{content:"";transform-origin:50%;pointer-events:none;justify-self:flex-end;width:.375rem;height:.375rem;transition-property:rotate,translate;transition-duration:.2s;display:block;translate:0 -1px;rotate:-135deg;box-shadow:inset 2px 2px}}& :where(li>details[open]>summary):after,& :where(li>.menu-dropdown-toggle.menu-dropdown-show):after{translate:0 1px;rotate:45deg}& :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn){&.menu-focus,&:focus-visible{cursor:pointer;background-color:color-mix(in oklab,var(--color-base-content)10%,transparent);color:var(--color-base-content);--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}& :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title):not(.menu-active,:active,.btn):hover,li:not(.menu-title,.disabled)>details>summary:not(.menu-title):not(.menu-active,:active,.btn):hover){cursor:pointer;background-color:color-mix(in oklab,var(--color-base-content)10%,transparent);--tw-outline-style:none;outline-style:none;box-shadow:inset 0 1px oklch(0% 0 0/.01),inset 0 -1px oklch(100% 0 0/.01);@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}& :where(li:empty){background-color:var(--color-base-content);opacity:.1;height:1px;margin:.5rem 1rem}& :where(li){flex-flow:column wrap;flex-shrink:0;align-items:stretch;display:flex;position:relative;& .badge{justify-self:flex-end}&>:not(ul,.menu-title,details,.btn):active,&>:not(ul,.menu-title,details,.btn).menu-active,&>details>summary:active{--tw-outline-style:none;color:var(--menu-active-fg);background-color:var(--menu-active-bg);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}&:not(&:active){box-shadow:0 2px calc(var(--depth)*3px)-2px var(--menu-active-bg)}}&.menu-disabled{pointer-events:none;color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}& .dropdown:focus-within{& .menu-dropdown-toggle:after{translate:0 1px;rotate:45deg}}& .dropdown-content{margin-top:.5rem;padding:.5rem;&:before{display:none}}}.menu-title{color:color-mix(in oklab,var(--color-base-content)40%,transparent);padding-block:.5rem;padding-inline:.75rem;font-size:.875rem;font-weight:600}.menu-horizontal{flex-direction:row;display:inline-flex;&>li:not(.menu-title)>details>ul{margin-inline-start:0;margin-top:1rem;padding-block:.5rem;padding-inline-end:.5rem;position:absolute}&>li>details>ul{&:before{content:none}}:where(&>li:not(.menu-title)>details>ul){border-radius:var(--radius-box);background-color:var(--color-base-100);box-shadow:0 1px 3px oklch(0% 0 0/.1),0 1px 2px -1px oklch(0% 0 0/.1)}}.menu-vertical{flex-direction:column;display:inline-flex;&>li:not(.menu-title)>details>ul{margin-inline-start:1rem;margin-top:0;padding-block:0;padding-inline-end:0;position:relative}}.menu-xs{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.25rem;padding-inline:.5rem;font-size:.6875rem}& .menu-title{padding-block:.25rem;padding-inline:.5rem}}.menu-sm{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.25rem;padding-inline:.625rem;font-size:.75rem}& .menu-title{padding-block:.5rem;padding-inline:.75rem}}.menu-md{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:.75rem;font-size:.875rem}& .menu-title{padding-block:.5rem;padding-inline:.75rem}}.menu-lg{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:1rem;font-size:1.125rem}& .menu-title{padding-block:.75rem;padding-inline:1.5rem}}.menu-xl{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:1.25rem;font-size:1.375rem}& .menu-title{padding-block:.75rem;padding-inline:1.5rem}}@media (width>=640px){.sm\:menu{--menu-active-fg:var(--color-neutral-content);--menu-active-bg:var(--color-neutral);flex-flow:column wrap;width:fit-content;padding:.5rem;font-size:.875rem;display:flex;& :where(li ul){white-space:nowrap;margin-inline-start:1rem;padding-inline-start:.5rem;position:relative;&:before{inset-inline-start:0;background-color:var(--color-base-content);opacity:.1;width:var(--border);content:"";position:absolute;top:.75rem;bottom:.75rem}}& :where(li>.menu-dropdown:not(.menu-dropdown-show)){display:none}& :where(li:not(.menu-title)>:not(ul,details,.menu-title,.btn)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);text-align:start;text-wrap:balance;user-select:none;grid-auto-columns:minmax(auto,max-content) auto max-content;grid-auto-flow:column;align-content:flex-start;align-items:center;gap:.5rem;padding-block:.375rem;padding-inline:.75rem;transition-property:color,background-color,box-shadow;transition-duration:.2s;transition-timing-function:cubic-bezier(0,0,.2,1);display:grid}& :where(li>details>summary){--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}&::-webkit-details-marker{display:none}}& :where(li>details>summary),& :where(li>.menu-dropdown-toggle){&:after{content:"";transform-origin:50%;pointer-events:none;justify-self:flex-end;width:.375rem;height:.375rem;transition-property:rotate,translate;transition-duration:.2s;display:block;translate:0 -1px;rotate:-135deg;box-shadow:inset 2px 2px}}& :where(li>details[open]>summary):after,& :where(li>.menu-dropdown-toggle.menu-dropdown-show):after{translate:0 1px;rotate:45deg}& :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn){&.menu-focus,&:focus-visible{cursor:pointer;background-color:color-mix(in oklab,var(--color-base-content)10%,transparent);color:var(--color-base-content);--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}& :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title):not(.menu-active,:active,.btn):hover,li:not(.menu-title,.disabled)>details>summary:not(.menu-title):not(.menu-active,:active,.btn):hover){cursor:pointer;background-color:color-mix(in oklab,var(--color-base-content)10%,transparent);--tw-outline-style:none;outline-style:none;box-shadow:inset 0 1px oklch(0% 0 0/.01),inset 0 -1px oklch(100% 0 0/.01);@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}& :where(li:empty){background-color:var(--color-base-content);opacity:.1;height:1px;margin:.5rem 1rem}& :where(li){flex-flow:column wrap;flex-shrink:0;align-items:stretch;display:flex;position:relative;& .badge{justify-self:flex-end}&>:not(ul,.menu-title,details,.btn):active,&>:not(ul,.menu-title,details,.btn).menu-active,&>details>summary:active{--tw-outline-style:none;color:var(--menu-active-fg);background-color:var(--menu-active-bg);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}&:not(&:active){box-shadow:0 2px calc(var(--depth)*3px)-2px var(--menu-active-bg)}}&.menu-disabled{pointer-events:none;color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}& .dropdown:focus-within{& .menu-dropdown-toggle:after{translate:0 1px;rotate:45deg}}& .dropdown-content{margin-top:.5rem;padding:.5rem;&:before{display:none}}}.sm\:menu-title{color:color-mix(in oklab,var(--color-base-content)40%,transparent);padding-block:.5rem;padding-inline:.75rem;font-size:.875rem;font-weight:600}.sm\:menu-horizontal{flex-direction:row;display:inline-flex;&>li:not(.menu-title)>details>ul{margin-inline-start:0;margin-top:1rem;padding-block:.5rem;padding-inline-end:.5rem;position:absolute}&>li>details>ul{&:before{content:none}}:where(&>li:not(.menu-title)>details>ul){border-radius:var(--radius-box);background-color:var(--color-base-100);box-shadow:0 1px 3px oklch(0% 0 0/.1),0 1px 2px -1px oklch(0% 0 0/.1)}}.sm\:menu-vertical{flex-direction:column;display:inline-flex;&>li:not(.menu-title)>details>ul{margin-inline-start:1rem;margin-top:0;padding-block:0;padding-inline-end:0;position:relative}}.sm\:menu-xs{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.25rem;padding-inline:.5rem;font-size:.6875rem}& .menu-title{padding-block:.25rem;padding-inline:.5rem}}.sm\:menu-sm{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.25rem;padding-inline:.625rem;font-size:.75rem}& .menu-title{padding-block:.5rem;padding-inline:.75rem}}.sm\:menu-md{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:.75rem;font-size:.875rem}& .menu-title{padding-block:.5rem;padding-inline:.75rem}}.sm\:menu-lg{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:1rem;font-size:1.125rem}& .menu-title{padding-block:.75rem;padding-inline:1.5rem}}.sm\:menu-xl{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:1.25rem;font-size:1.375rem}& .menu-title{padding-block:.75rem;padding-inline:1.5rem}}}@media (width>=768px){.md\:menu{--menu-active-fg:var(--color-neutral-content);--menu-active-bg:var(--color-neutral);flex-flow:column wrap;width:fit-content;padding:.5rem;font-size:.875rem;display:flex;& :where(li ul){white-space:nowrap;margin-inline-start:1rem;padding-inline-start:.5rem;position:relative;&:before{inset-inline-start:0;background-color:var(--color-base-content);opacity:.1;width:var(--border);content:"";position:absolute;top:.75rem;bottom:.75rem}}& :where(li>.menu-dropdown:not(.menu-dropdown-show)){display:none}& :where(li:not(.menu-title)>:not(ul,details,.menu-title,.btn)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);text-align:start;text-wrap:balance;user-select:none;grid-auto-columns:minmax(auto,max-content) auto max-content;grid-auto-flow:column;align-content:flex-start;align-items:center;gap:.5rem;padding-block:.375rem;padding-inline:.75rem;transition-property:color,background-color,box-shadow;transition-duration:.2s;transition-timing-function:cubic-bezier(0,0,.2,1);display:grid}& :where(li>details>summary){--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}&::-webkit-details-marker{display:none}}& :where(li>details>summary),& :where(li>.menu-dropdown-toggle){&:after{content:"";transform-origin:50%;pointer-events:none;justify-self:flex-end;width:.375rem;height:.375rem;transition-property:rotate,translate;transition-duration:.2s;display:block;translate:0 -1px;rotate:-135deg;box-shadow:inset 2px 2px}}& :where(li>details[open]>summary):after,& :where(li>.menu-dropdown-toggle.menu-dropdown-show):after{translate:0 1px;rotate:45deg}& :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn){&.menu-focus,&:focus-visible{cursor:pointer;background-color:color-mix(in oklab,var(--color-base-content)10%,transparent);color:var(--color-base-content);--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}& :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title):not(.menu-active,:active,.btn):hover,li:not(.menu-title,.disabled)>details>summary:not(.menu-title):not(.menu-active,:active,.btn):hover){cursor:pointer;background-color:color-mix(in oklab,var(--color-base-content)10%,transparent);--tw-outline-style:none;outline-style:none;box-shadow:inset 0 1px oklch(0% 0 0/.01),inset 0 -1px oklch(100% 0 0/.01);@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}& :where(li:empty){background-color:var(--color-base-content);opacity:.1;height:1px;margin:.5rem 1rem}& :where(li){flex-flow:column wrap;flex-shrink:0;align-items:stretch;display:flex;position:relative;& .badge{justify-self:flex-end}&>:not(ul,.menu-title,details,.btn):active,&>:not(ul,.menu-title,details,.btn).menu-active,&>details>summary:active{--tw-outline-style:none;color:var(--menu-active-fg);background-color:var(--menu-active-bg);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}&:not(&:active){box-shadow:0 2px calc(var(--depth)*3px)-2px var(--menu-active-bg)}}&.menu-disabled{pointer-events:none;color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}& .dropdown:focus-within{& .menu-dropdown-toggle:after{translate:0 1px;rotate:45deg}}& .dropdown-content{margin-top:.5rem;padding:.5rem;&:before{display:none}}}.md\:menu-title{color:color-mix(in oklab,var(--color-base-content)40%,transparent);padding-block:.5rem;padding-inline:.75rem;font-size:.875rem;font-weight:600}.md\:menu-horizontal{flex-direction:row;display:inline-flex;&>li:not(.menu-title)>details>ul{margin-inline-start:0;margin-top:1rem;padding-block:.5rem;padding-inline-end:.5rem;position:absolute}&>li>details>ul{&:before{content:none}}:where(&>li:not(.menu-title)>details>ul){border-radius:var(--radius-box);background-color:var(--color-base-100);box-shadow:0 1px 3px oklch(0% 0 0/.1),0 1px 2px -1px oklch(0% 0 0/.1)}}.md\:menu-vertical{flex-direction:column;display:inline-flex;&>li:not(.menu-title)>details>ul{margin-inline-start:1rem;margin-top:0;padding-block:0;padding-inline-end:0;position:relative}}.md\:menu-xs{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.25rem;padding-inline:.5rem;font-size:.6875rem}& .menu-title{padding-block:.25rem;padding-inline:.5rem}}.md\:menu-sm{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.25rem;padding-inline:.625rem;font-size:.75rem}& .menu-title{padding-block:.5rem;padding-inline:.75rem}}.md\:menu-md{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:.75rem;font-size:.875rem}& .menu-title{padding-block:.5rem;padding-inline:.75rem}}.md\:menu-lg{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:1rem;font-size:1.125rem}& .menu-title{padding-block:.75rem;padding-inline:1.5rem}}.md\:menu-xl{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:1.25rem;font-size:1.375rem}& .menu-title{padding-block:.75rem;padding-inline:1.5rem}}}@media (width>=1024px){.lg\:menu{--menu-active-fg:var(--color-neutral-content);--menu-active-bg:var(--color-neutral);flex-flow:column wrap;width:fit-content;padding:.5rem;font-size:.875rem;display:flex;& :where(li ul){white-space:nowrap;margin-inline-start:1rem;padding-inline-start:.5rem;position:relative;&:before{inset-inline-start:0;background-color:var(--color-base-content);opacity:.1;width:var(--border);content:"";position:absolute;top:.75rem;bottom:.75rem}}& :where(li>.menu-dropdown:not(.menu-dropdown-show)){display:none}& :where(li:not(.menu-title)>:not(ul,details,.menu-title,.btn)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);text-align:start;text-wrap:balance;user-select:none;grid-auto-columns:minmax(auto,max-content) auto max-content;grid-auto-flow:column;align-content:flex-start;align-items:center;gap:.5rem;padding-block:.375rem;padding-inline:.75rem;transition-property:color,background-color,box-shadow;transition-duration:.2s;transition-timing-function:cubic-bezier(0,0,.2,1);display:grid}& :where(li>details>summary){--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}&::-webkit-details-marker{display:none}}& :where(li>details>summary),& :where(li>.menu-dropdown-toggle){&:after{content:"";transform-origin:50%;pointer-events:none;justify-self:flex-end;width:.375rem;height:.375rem;transition-property:rotate,translate;transition-duration:.2s;display:block;translate:0 -1px;rotate:-135deg;box-shadow:inset 2px 2px}}& :where(li>details[open]>summary):after,& :where(li>.menu-dropdown-toggle.menu-dropdown-show):after{translate:0 1px;rotate:45deg}& :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn){&.menu-focus,&:focus-visible{cursor:pointer;background-color:color-mix(in oklab,var(--color-base-content)10%,transparent);color:var(--color-base-content);--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}& :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title):not(.menu-active,:active,.btn):hover,li:not(.menu-title,.disabled)>details>summary:not(.menu-title):not(.menu-active,:active,.btn):hover){cursor:pointer;background-color:color-mix(in oklab,var(--color-base-content)10%,transparent);--tw-outline-style:none;outline-style:none;box-shadow:inset 0 1px oklch(0% 0 0/.01),inset 0 -1px oklch(100% 0 0/.01);@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}& :where(li:empty){background-color:var(--color-base-content);opacity:.1;height:1px;margin:.5rem 1rem}& :where(li){flex-flow:column wrap;flex-shrink:0;align-items:stretch;display:flex;position:relative;& .badge{justify-self:flex-end}&>:not(ul,.menu-title,details,.btn):active,&>:not(ul,.menu-title,details,.btn).menu-active,&>details>summary:active{--tw-outline-style:none;color:var(--menu-active-fg);background-color:var(--menu-active-bg);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}&:not(&:active){box-shadow:0 2px calc(var(--depth)*3px)-2px var(--menu-active-bg)}}&.menu-disabled{pointer-events:none;color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}& .dropdown:focus-within{& .menu-dropdown-toggle:after{translate:0 1px;rotate:45deg}}& .dropdown-content{margin-top:.5rem;padding:.5rem;&:before{display:none}}}.lg\:menu-title{color:color-mix(in oklab,var(--color-base-content)40%,transparent);padding-block:.5rem;padding-inline:.75rem;font-size:.875rem;font-weight:600}.lg\:menu-horizontal{flex-direction:row;display:inline-flex;&>li:not(.menu-title)>details>ul{margin-inline-start:0;margin-top:1rem;padding-block:.5rem;padding-inline-end:.5rem;position:absolute}&>li>details>ul{&:before{content:none}}:where(&>li:not(.menu-title)>details>ul){border-radius:var(--radius-box);background-color:var(--color-base-100);box-shadow:0 1px 3px oklch(0% 0 0/.1),0 1px 2px -1px oklch(0% 0 0/.1)}}.lg\:menu-vertical{flex-direction:column;display:inline-flex;&>li:not(.menu-title)>details>ul{margin-inline-start:1rem;margin-top:0;padding-block:0;padding-inline-end:0;position:relative}}.lg\:menu-xs{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.25rem;padding-inline:.5rem;font-size:.6875rem}& .menu-title{padding-block:.25rem;padding-inline:.5rem}}.lg\:menu-sm{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.25rem;padding-inline:.625rem;font-size:.75rem}& .menu-title{padding-block:.5rem;padding-inline:.75rem}}.lg\:menu-md{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:.75rem;font-size:.875rem}& .menu-title{padding-block:.5rem;padding-inline:.75rem}}.lg\:menu-lg{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:1rem;font-size:1.125rem}& .menu-title{padding-block:.75rem;padding-inline:1.5rem}}.lg\:menu-xl{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:1.25rem;font-size:1.375rem}& .menu-title{padding-block:.75rem;padding-inline:1.5rem}}}@media (width>=1280px){.xl\:menu{--menu-active-fg:var(--color-neutral-content);--menu-active-bg:var(--color-neutral);flex-flow:column wrap;width:fit-content;padding:.5rem;font-size:.875rem;display:flex;& :where(li ul){white-space:nowrap;margin-inline-start:1rem;padding-inline-start:.5rem;position:relative;&:before{inset-inline-start:0;background-color:var(--color-base-content);opacity:.1;width:var(--border);content:"";position:absolute;top:.75rem;bottom:.75rem}}& :where(li>.menu-dropdown:not(.menu-dropdown-show)){display:none}& :where(li:not(.menu-title)>:not(ul,details,.menu-title,.btn)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);text-align:start;text-wrap:balance;user-select:none;grid-auto-columns:minmax(auto,max-content) auto max-content;grid-auto-flow:column;align-content:flex-start;align-items:center;gap:.5rem;padding-block:.375rem;padding-inline:.75rem;transition-property:color,background-color,box-shadow;transition-duration:.2s;transition-timing-function:cubic-bezier(0,0,.2,1);display:grid}& :where(li>details>summary){--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}&::-webkit-details-marker{display:none}}& :where(li>details>summary),& :where(li>.menu-dropdown-toggle){&:after{content:"";transform-origin:50%;pointer-events:none;justify-self:flex-end;width:.375rem;height:.375rem;transition-property:rotate,translate;transition-duration:.2s;display:block;translate:0 -1px;rotate:-135deg;box-shadow:inset 2px 2px}}& :where(li>details[open]>summary):after,& :where(li>.menu-dropdown-toggle.menu-dropdown-show):after{translate:0 1px;rotate:45deg}& :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn){&.menu-focus,&:focus-visible{cursor:pointer;background-color:color-mix(in oklab,var(--color-base-content)10%,transparent);color:var(--color-base-content);--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}& :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title):not(.menu-active,:active,.btn):hover,li:not(.menu-title,.disabled)>details>summary:not(.menu-title):not(.menu-active,:active,.btn):hover){cursor:pointer;background-color:color-mix(in oklab,var(--color-base-content)10%,transparent);--tw-outline-style:none;outline-style:none;box-shadow:inset 0 1px oklch(0% 0 0/.01),inset 0 -1px oklch(100% 0 0/.01);@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}& :where(li:empty){background-color:var(--color-base-content);opacity:.1;height:1px;margin:.5rem 1rem}& :where(li){flex-flow:column wrap;flex-shrink:0;align-items:stretch;display:flex;position:relative;& .badge{justify-self:flex-end}&>:not(ul,.menu-title,details,.btn):active,&>:not(ul,.menu-title,details,.btn).menu-active,&>details>summary:active{--tw-outline-style:none;color:var(--menu-active-fg);background-color:var(--menu-active-bg);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}&:not(&:active){box-shadow:0 2px calc(var(--depth)*3px)-2px var(--menu-active-bg)}}&.menu-disabled{pointer-events:none;color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}& .dropdown:focus-within{& .menu-dropdown-toggle:after{translate:0 1px;rotate:45deg}}& .dropdown-content{margin-top:.5rem;padding:.5rem;&:before{display:none}}}.xl\:menu-title{color:color-mix(in oklab,var(--color-base-content)40%,transparent);padding-block:.5rem;padding-inline:.75rem;font-size:.875rem;font-weight:600}.xl\:menu-horizontal{flex-direction:row;display:inline-flex;&>li:not(.menu-title)>details>ul{margin-inline-start:0;margin-top:1rem;padding-block:.5rem;padding-inline-end:.5rem;position:absolute}&>li>details>ul{&:before{content:none}}:where(&>li:not(.menu-title)>details>ul){border-radius:var(--radius-box);background-color:var(--color-base-100);box-shadow:0 1px 3px oklch(0% 0 0/.1),0 1px 2px -1px oklch(0% 0 0/.1)}}.xl\:menu-vertical{flex-direction:column;display:inline-flex;&>li:not(.menu-title)>details>ul{margin-inline-start:1rem;margin-top:0;padding-block:0;padding-inline-end:0;position:relative}}.xl\:menu-xs{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.25rem;padding-inline:.5rem;font-size:.6875rem}& .menu-title{padding-block:.25rem;padding-inline:.5rem}}.xl\:menu-sm{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.25rem;padding-inline:.625rem;font-size:.75rem}& .menu-title{padding-block:.5rem;padding-inline:.75rem}}.xl\:menu-md{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:.75rem;font-size:.875rem}& .menu-title{padding-block:.5rem;padding-inline:.75rem}}.xl\:menu-lg{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:1rem;font-size:1.125rem}& .menu-title{padding-block:.75rem;padding-inline:1.5rem}}.xl\:menu-xl{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:1.25rem;font-size:1.375rem}& .menu-title{padding-block:.75rem;padding-inline:1.5rem}}}@media (width>=1536px){.\32 xl\:menu{--menu-active-fg:var(--color-neutral-content);--menu-active-bg:var(--color-neutral);flex-flow:column wrap;width:fit-content;padding:.5rem;font-size:.875rem;display:flex;& :where(li ul){white-space:nowrap;margin-inline-start:1rem;padding-inline-start:.5rem;position:relative;&:before{inset-inline-start:0;background-color:var(--color-base-content);opacity:.1;width:var(--border);content:"";position:absolute;top:.75rem;bottom:.75rem}}& :where(li>.menu-dropdown:not(.menu-dropdown-show)){display:none}& :where(li:not(.menu-title)>:not(ul,details,.menu-title,.btn)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);text-align:start;text-wrap:balance;user-select:none;grid-auto-columns:minmax(auto,max-content) auto max-content;grid-auto-flow:column;align-content:flex-start;align-items:center;gap:.5rem;padding-block:.375rem;padding-inline:.75rem;transition-property:color,background-color,box-shadow;transition-duration:.2s;transition-timing-function:cubic-bezier(0,0,.2,1);display:grid}& :where(li>details>summary){--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}&::-webkit-details-marker{display:none}}& :where(li>details>summary),& :where(li>.menu-dropdown-toggle){&:after{content:"";transform-origin:50%;pointer-events:none;justify-self:flex-end;width:.375rem;height:.375rem;transition-property:rotate,translate;transition-duration:.2s;display:block;translate:0 -1px;rotate:-135deg;box-shadow:inset 2px 2px}}& :where(li>details[open]>summary):after,& :where(li>.menu-dropdown-toggle.menu-dropdown-show):after{translate:0 1px;rotate:45deg}& :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn){&.menu-focus,&:focus-visible{cursor:pointer;background-color:color-mix(in oklab,var(--color-base-content)10%,transparent);color:var(--color-base-content);--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}& :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title):not(.menu-active,:active,.btn):hover,li:not(.menu-title,.disabled)>details>summary:not(.menu-title):not(.menu-active,:active,.btn):hover){cursor:pointer;background-color:color-mix(in oklab,var(--color-base-content)10%,transparent);--tw-outline-style:none;outline-style:none;box-shadow:inset 0 1px oklch(0% 0 0/.01),inset 0 -1px oklch(100% 0 0/.01);@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}& :where(li:empty){background-color:var(--color-base-content);opacity:.1;height:1px;margin:.5rem 1rem}& :where(li){flex-flow:column wrap;flex-shrink:0;align-items:stretch;display:flex;position:relative;& .badge{justify-self:flex-end}&>:not(ul,.menu-title,details,.btn):active,&>:not(ul,.menu-title,details,.btn).menu-active,&>details>summary:active{--tw-outline-style:none;color:var(--menu-active-fg);background-color:var(--menu-active-bg);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}&:not(&:active){box-shadow:0 2px calc(var(--depth)*3px)-2px var(--menu-active-bg)}}&.menu-disabled{pointer-events:none;color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}& .dropdown:focus-within{& .menu-dropdown-toggle:after{translate:0 1px;rotate:45deg}}& .dropdown-content{margin-top:.5rem;padding:.5rem;&:before{display:none}}}.\32 xl\:menu-title{color:color-mix(in oklab,var(--color-base-content)40%,transparent);padding-block:.5rem;padding-inline:.75rem;font-size:.875rem;font-weight:600}.\32 xl\:menu-horizontal{flex-direction:row;display:inline-flex;&>li:not(.menu-title)>details>ul{margin-inline-start:0;margin-top:1rem;padding-block:.5rem;padding-inline-end:.5rem;position:absolute}&>li>details>ul{&:before{content:none}}:where(&>li:not(.menu-title)>details>ul){border-radius:var(--radius-box);background-color:var(--color-base-100);box-shadow:0 1px 3px oklch(0% 0 0/.1),0 1px 2px -1px oklch(0% 0 0/.1)}}.\32 xl\:menu-vertical{flex-direction:column;display:inline-flex;&>li:not(.menu-title)>details>ul{margin-inline-start:1rem;margin-top:0;padding-block:0;padding-inline-end:0;position:relative}}.\32 xl\:menu-xs{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.25rem;padding-inline:.5rem;font-size:.6875rem}& .menu-title{padding-block:.25rem;padding-inline:.5rem}}.\32 xl\:menu-sm{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.25rem;padding-inline:.625rem;font-size:.75rem}& .menu-title{padding-block:.5rem;padding-inline:.75rem}}.\32 xl\:menu-md{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:.75rem;font-size:.875rem}& .menu-title{padding-block:.5rem;padding-inline:.75rem}}.\32 xl\:menu-lg{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:1rem;font-size:1.125rem}& .menu-title{padding-block:.75rem;padding-inline:1.5rem}}.\32 xl\:menu-xl{& :where(li:not(.menu-title)>:not(ul,details,.menu-title)),& :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);padding-block:.375rem;padding-inline:1.25rem;font-size:1.375rem}& .menu-title{padding-block:.75rem;padding-inline:1.5rem}}}}